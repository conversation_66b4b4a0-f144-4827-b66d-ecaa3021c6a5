/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import moment from 'moment';
import { withTranslation } from 'react-i18next';
import { get, capitalize } from 'lodash';
import { calculateorgSize } from 'utils/helper';
import {
  getManageSubscriptionLink,
  getSubscription,
  setSubscriptionJourneyModal,
} from 'reducer/subscription';
import Router from 'next/router';
import style from '../../project/overview/style/projectDashboard.module.scss';
import Styles from '../style/myaccount.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import Button from 'sharedComponents/Button/button';
import products from 'configuration/products.json';

class MyAccountView extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      subscription: {},
      product: get(products, process.env.StripPlanPath, {}),
      loading: true,
    };
  }

  componentDidMount() {
    const { getSubscription } = this.props;
    window.scrollTo(0, 0);
    const currentPath = Router.pathname;
    getSubscription(currentPath)
      .then((data) => {
        this.setState({
          subscription: data.userMeta,
          loading: false,
        });
      })
      .catch((err) => {
        console.log(err);
        this.setState({
          loading: false,
        });
      });
  }

  // method to send the user to correct imdb url
  checkUrl = (url) => {
    let openUrl = url;
    if (!url.match(/^https?:\/\//i)) {
      openUrl = `https://${url}`;
    }
    window.open(openUrl);
  };

  closeModal = () => {
    this.props.setSubscriptionJourneyModal({
      modalStatus: false,
      feature: '',
      onComplete: () => { },
    });
  };

  goToUpgradeSubscription = async () => {
    const { setSubscriptionJourneyModal } = this.props;
    if (
      get(this.state.subscription, 'isLegacy') &&
      process.env.legacyCampaignUrl
    ) {
      Router.push(
        `${process.env.LoginUrl}/offer/${process.env.legacyCampaignUrl}`,
      );
    } else {
      setSubscriptionJourneyModal({
        modalStatus: true,
        feature: 'proTrial',
        cameFrom: 'myaccount',
        onComplete: this.closeModal,
      });
    }
  };

  showTimeDiff = () => {
    const { subscription } = this.state;
    const expiredTime = moment(get(subscription, 'expired') * 1000);
    const currentTime = moment();

    const days = expiredTime.diff(currentTime, 'days');
    if (days > 0) {
      return `${days} days`;
    } else {
      const hours = expiredTime.diff(currentTime, 'hours');
      if (hours > 0) {
        return `${hours} hours`;
      } else {
        const minutes = expiredTime.diff(currentTime, 'minutes');
        return `${minutes < 0 ? 0 : minutes} minutes`;
      }
    }
  };

  goToManageSubscription = async () => {
    await this.props
      .getManageSubscriptionLink()
      .then((url) => {
        window.open(url, '_blank');
      })
      .catch((err) => {
        console.log(err);
      });
  };

  render() {
    const { userData, currentBillingCycle } = this.props;
    const { subscription, product, loading } = this.state;
    const profilePic = get(userData, 'profile.profileImage');
    const interests = get(userData, 'profile.interests', []);
    const socialMedia = get(userData, 'profile.socialMediaUrls', false);
    const imdbData = get(userData, 'profile.IMBDlink', false);
    const imdbUrls = get(socialMedia, 'imdb', imdbData);
    const privacy = get(userData, 'profile.privacy', []);
    const contactDetails = get(userData, 'profile.contact', false);
    let interestList = '';

    if (interests) {
      interestList = interests.map((item) => {
        return (
          <div className={`${Styles.intrestList} mx-0 mr-3`} key={item}>
            {item.text}
          </div>
        );
      });
    }
    const interestLength = get(userData, 'profile.interests.length');
    const organisationType = get(userData, 'profile.organisationType', '');
    const occupationType = get(userData, 'profile.occupationType', '');
    const organisationLogo = get(userData, 'profile.organisationLogo');
    const orgSize = get(userData, 'profile.organisationSize', '');

    //     function to go to subscriptions
    // const goToUpgradeSubscription = () => {
    //   Router.push(`${process.env.LoginUrl}/payments`);
    // };

    const isFreeMember =
      this.props.currentPlan === 'free' ||
      this.props.currentPlan === '' ||
      this.props.currentPlan === 'legacy';

    return (
      <div
        className={`${Styles.myProfileContainer} container-fluid p-3 p-sm-3 p-md-5 p-lg-5 mb-5`}
      >
        <div className="row" style={{ gap: '20px' }}>
          <div className="col-12">
            <h2 className="text-primary m-0">User Details</h2>
          </div>
          <div className="col-12 col-sm-12 col-md-3 col-lg-3 pl-2 d-flex justify-content-between m-0">
            {profilePic ? (
              <img
                src={profilePic}
                alt="profile"
                className={`${Styles.img}`}
                data-cy="creativeImage"
              />
            ) : (
              <img
                alt="profile"
                className={`${Styles.img}`}
                src="/assets/jpg/Placeholder_Avatar_320.jpg"
              />
            )}
          </div>
          {subscription && this.props.currentPlan && (
            <div className="col-12 col-sm-12 col-md-5 col-lg-5 m-0">
              <div className="col-6 px-0 m-0">
                <h5
                  className={`fs-14 text-center mx-0 mt-0 mb-20 ${loading
                    ? 'bg-transparent'
                    : !isFreeMember
                      ? 'bg-success'
                      : 'badge-light '
                    }`}
                  style={{
                    borderRadius: 'none',
                    padding: '3px 8px',
                  }}
                >
                  {loading
                    ? ''
                    : !isFreeMember
                      ? `SMASH ${this.props.currentPlan}`
                      : this.props.currentPlan === 'legacy'
                        ? 'SMASH LEGACY'
                        : 'SMASH FREE'}
                </h5>
              </div>
              <p className="text-primary p1 text-break mx-0 mt-0 mb-20">
                {`${get(userData, 'profile.name.firstName', '')} 
                      ${get(userData, 'profile.name.lastName', '')}`}
              </p>

              <p className="text-primary p1 text-break m-0">
                {occupationType === 'Other'
                  ? get(userData, 'profile.otherOccupation', '')
                  : occupationType}
              </p>
            </div>
          )}
          <div className="col-12 col-sm-12 col-md-4 col-lg-4">
            <p className="text-primary p1 text-break mx-0 mt-0 mb-20">
              {get(userData, 'profile.city.address', '')}
            </p>
            <p className="text-primary p1 text-break">
              {get(userData, 'email', '')}
            </p>
          </div>
        </div>
        <hr />
        <div className="my-32 row" style={{ gap: '20px' }}>
          <h4 className="text-primary col-12 m-0">Organisation Details</h4>
          {/* <div className="d-flex my-32"> */}
          <div className="col-12 col-sm-12 col-md-3 col-lg-3 px-0 m-0">
            {organisationLogo ? (
              <img
                src={organisationLogo}
                alt="profile"
                className={`${Styles.img} ml-2 ml-sm-2 ml-md-0 ml-lg-0`}
                data-cy="creativeImage"
              />
            ) : (
              <img
                alt="profile"
                className={`${Styles.img} ml-2 ml-sm-2 ml-md-0 ml-lg-0`}
                src="/assets/jpg/Placeholder_Avatar_320.jpg"
              />
            )}
          </div>
          <div className="col-12 col-sm-12 col-md-5 col-lg-5 m-0">
            <p className="text-primary p1 m-0 text-break">
              {get(userData, 'profile.organisation', '')}
            </p>
            <p className="text-primary p1 mt-20 mx-0 mb-0 text-break">
              {organisationType === 'Other'
                ? `${get(userData, 'profile.otherOrganisation', '')}, ${calculateorgSize(orgSize)}`
                : `${organisationType}, ${calculateorgSize(orgSize)}`}
            </p>
            {/* <p className="text-primary p1">
                  {get(userData, 'profile.jobTitle', '')}{' '}
                </p> */}
          </div>
          {/* <div className="col-4 m-0">
            <p className="text-primary p1 text-break m-0">
              {calculateorgSize(orgSize)}
            </p>
          </div> */}
          {/* </div> */}
          <div className="col-12">
            <p className="text-primary p1 text-break m-0">
              {get(userData, 'profile.discovererProfile')}
            </p>
          </div>
        </div>
        {interestLength > 0 && (
          <div>
            <hr />
            <div className="my-32">
              <h4 className="text-primary">I’m interested in</h4>
              <div className="d-flex flex-wrap">{interestList}</div>
              <p className="text-primary p1 text-break">
                {interestLength === 0 ? 'No Interests' : ''}
              </p>
            </div>
          </div>
        )}
        <div>
          <hr style={{ marginTop: '31px' }} />
          <div className="my-32">
            <h4 className="text-primary text-left mb-3">Subscription</h4>
            {this.props.currentPlan !== 'free' &&
              this.props.currentPlan !== 'legacy' ? (
              <div className="d-flex col-12 col-sm-12 col-md-8 col-lg-8 p-0 m-0 justify-content-between">
                <div className="text-primary text-left ">
                  <p className="fs-12 mb-1">Membership</p>
                  <p className="fs-16">
                    {`SMASH ${this.props.currentPlan !== '' ? this.props.currentPlan.charAt(0).toUpperCase() + this.props.currentPlan.slice(1) : ''}`}
                  </p>
                </div>
                {/* <div className="text-primary text-left ">
                    <p className="fs-12 mb-1">Current Status</p>
                    <p className="fs-16 text-uppercase">
                      {subscription.status === 'draft'
                        ? 'active'
                        : subscription.status}
                    </p>
                  </div> */}
                {(get(subscription, 'amount') !== 0 && get(subscription, 'bacsPaymentStatus') === 'processing') &&
                  !this.props.isTrial &&
                  get(subscription, 'amount') !== undefined && (
                    <div className="text-primary text-left mx-24">
                      <p className="fs-12 mb-1">
                        Status
                      </p>
                      <p>Payment Processing</p>
                    </div>
                  )}
                {(get(subscription, 'amount') !== 0 && get(subscription, 'bacsPaymentStatus') !== 'processing') &&
                  !this.props.isTrial &&
                  get(subscription, 'amount') !== undefined && (
                    <div className="text-primary text-left mx-24">
                      <p className="fs-12 mb-1">
                        {currentBillingCycle === 'yearly'
                          ? capitalize('yearly')
                          : capitalize(get(product, 'plan'))}{' '}
                        payment
                      </p>
                      {this.props.currentPlan === 'free' ||
                        this.props.currentPlan === 'legacy'
                        ? 'NA'
                        : `£${(get(subscription, 'amount') / 100).toFixed(2)}`}
                    </div>
                  )}
                {get(subscription, 'expired') && (
                  <div className="text-primary text-left ">
                    <p className="fs-12 mb-1">
                      {subscription?.cancelAtPeriodEnd ||
                        get(subscription, 'status') === 'expired' ||
                        this.props.isTrial
                        ? moment(subscription.expired * 1000).diff(
                          moment(),
                          'days',
                        ) > 30
                          ? 'Expires on'
                          : 'Expires in'
                        : 'Renewal date'}
                    </p>
                    <p
                      className={`fs-16 ${(subscription?.cancelAtPeriodEnd ||
                        get(subscription, 'status') === 'expired' ||
                        this.props.isTrial) &&
                        moment(subscription.expired * 1000).diff(
                          moment(),
                          'days',
                        ) < 30
                        ? 'text-danger'
                        : ''
                        }`}
                    >
                      {(!subscription?.cancelAtPeriodEnd ||
                        moment(subscription.expired * 1000).diff(
                          moment(),
                          'days',
                        ) > 30) &&
                        get(subscription, 'status') !== 'expired' &&
                        !this.props.isTrial
                        ? this.props.currentPlan === 'enterprise'
                          ? moment(get(subscription, 'expired') * 1000).format(
                            'D MMMM YYYY',
                          )
                          : moment(get(subscription, 'expired') * 1000).format(
                            'D MMMM YYYY',
                          )
                        : get(subscription, 'status') === 'expired'
                          ? 'Expired'
                          : this.showTimeDiff()}
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-primary text-left ">
                <p className="fs-12 mb-1">Membership</p>
                <p>{`SMASH ${this.propscurrentPlan !== '' ? this.props.currentPlan.charAt(0).toUpperCase() + this.props.currentPlan.slice(1) : ''}`}</p>
              </div>
            )}
            <div className="d-flex justify-between">
              {!this.props.isTrial &&
                (this.props.currentPlan === 'pro' ||
                  this.props.currentPlan === 'enterprise') ? (
                <div className="p-0 mr-2">
                  <Button
                    btntype="submit"
                    customClass="waitListBtn"
                    clickHandler={this.goToManageSubscription}
                    buttonValue={
                      get(subscription, 'status') === 'expired'
                        ? 'Renew subscription'
                        : 'Manage Subscription on Stripe'
                    }
                    size="small"
                  />
                </div>
              ) : (
                <div className={'p-0'}>
                  <Button
                    btntype="submit"
                    customClass="waitListBtn"
                    clickHandler={this.goToUpgradeSubscription}
                    buttonValue={'Upgrade'}
                    size="small"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
        {(socialMedia || imdbUrls) && (
          <div>
            <hr />
            <div className="my-32">
              <h4 className="text-primary">Social Media</h4>
              <div className="d-flex">
                <div>
                  {imdbUrls && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/imdb.svg"
                      onClick={() => this.checkUrl(imdbUrls)}
                      alt="pc"
                    />
                  )}
                </div>
                <div>
                  {get(socialMedia, 'twitter', false) && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/x-twitter-icon.svg"
                      onClick={() => this.checkUrl(socialMedia.twitter)}
                      alt="pc"
                    />
                  )}
                </div>
                <div>
                  {get(socialMedia, 'facebook', false) && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/facebook.svg"
                      onClick={() => this.checkUrl(socialMedia.facebook)}
                      alt="pc"
                    />
                  )}
                </div>
                <div>
                  {get(socialMedia, 'instagram', false) && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/instagram.svg"
                      onClick={() => this.checkUrl(socialMedia.instagram)}
                      alt="pc"
                    />
                  )}
                </div>
                <div>
                  {get(socialMedia, 'tiktok', false) && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/tiktok.svg"
                      onClick={() => this.checkUrl(socialMedia.tiktok)}
                      alt="pc"
                    />
                  )}
                </div>
                <div>
                  {get(socialMedia, 'dot', false) && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/TheDots.svg"
                      onClick={() => this.checkUrl(socialMedia.dot)}
                      alt="pc"
                    />
                  )}
                </div>
                <div>
                  {get(socialMedia, 'linkedin', false) && (
                    <InlineSvg
                      className={`${style.pcImage}`}
                      src="/assets/svg/linked.svg"
                      onClick={() => this.checkUrl(socialMedia.linkedin)}
                      alt="pc"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        {privacy && (
          <div>
            <hr />
            <div className="my-32">
              <h4 className="text-primary">PRIVACY LEVEL</h4>
              <p className="text-primary text-left p1">{privacy}</p>
            </div>
          </div>
        )}
        {(get(contactDetails, 'email') ||
          get(contactDetails, 'phone') ||
          get(contactDetails, 'sms')) && (
            <div>
              <hr />
              <div className="mt-32">
                <h4 className="text-primary">Contact preferences</h4>
                <div className="d-flex flex-wrap">
                  {get(contactDetails, 'email') && (
                    <p className="text-primary text-left pr-3 p1 m-0">Email</p>
                  )}
                  {get(contactDetails, 'phone') && (
                    <p className="text-primary text-left pr-3 p1 m-0">Phone</p>
                  )}
                  {get(contactDetails, 'sms') && (
                    <p className="text-primary text-left p1 m-0">SMS</p>
                  )}
                </div>
              </div>
            </div>
          )}
      </div>
    );
  }
}

MyAccountView.propTypes = {
  userData: PropTypes.object.isRequired,
};
const mapStateToProps = (state) => ({
  currentPlan: state.subscription.currentPlan,
  accessibleFeature: state.subscription.accessibleFeature,
  isTrial: state.subscription.isTrial,
  currentBillingCycle: state.subscription.currentBillingCycle,
});

const mapDispatchToProps = (dispatch) => ({
  getManageSubscriptionLink: () => dispatch(getManageSubscriptionLink()),
  setSubscriptionJourneyModal: (payload) =>
    dispatch(setSubscriptionJourneyModal(payload)),
  getSubscription: (payload) => dispatch(getSubscription(payload)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation('common')(MyAccountView));
