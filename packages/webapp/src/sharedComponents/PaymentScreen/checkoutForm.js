import React, { useEffect, useRef, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  useStripe,
  useElements,
  PaymentElement,
} from '@stripe/react-stripe-js';
import {
  applyCouponCode,
  couponCodeValidation,
  getClientSecretKey,
  getPaymentIntent,
  removeCouponCode,
  setBillingCycle,
  setupIntent,
} from 'reducer/subscription';
import { useDispatch, useSelector } from 'react-redux';
import { getSubscription } from 'reducer/subscription';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import Loader from 'sharedComponents/loader';
import { get, isEmpty } from 'lodash';
import Button from 'sharedComponents/CascadeTemplate/Common/Button/button';
import Styles from './paymentForm.module.scss';
import Tag from 'sharedComponents/Tag/Tag';
import PlanDetails from 'sharedComponents/PricingCard/planDetails';
import { updateUserMeta } from 'reducer/auth';
// import PaymentScreen from './paymentScreen';
// import PlanDetails from 'sharedComponents/PricingCard/planDetails';

const stripePromise = loadStripe(process.env.stripePublicKey);

const PaymentScreenContent = ({
  clientSecret,
  setShowPaymentScreen,
  getPaymentIntent,
  couponCodeValidation,
  planDetail,
  applyCouponCode,
  setPriceText,
  selectedPlanDetalils,
  campaignDetails,
  setPromoCodeDetails,
  setBillingCycle,
  subscriptionId,
  setClientSecret,
  setupIntent,
  updateUserMeta
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const dispatch = useDispatch();
  const promoCodeDetailsRef = useRef(null);

  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMsg, setErrorMessage] = useState(null);
  const [promoCode, setPromoCode] = useState('');
  const [applyText, setApplyText] = useState(true);
  const [couponErrorMsg, setCouponErrorMsg] = useState(null);
  const [isCodeApplied, setIsCodeApplied] = useState(false);
  const [inCodeRemoving, setIsCodeRemoving] = useState(false);
  const [isOfferScreen, setIsOfferScreen] = useState(false);
  const [promoCodeId, setPromoCodeId] = useState(null);

  useEffect(() => {
    setIsOfferScreen(!isEmpty(campaignDetails));
    if (!isEmpty(campaignDetails)) {
      if (get(campaignDetails, 'couponCode', null)) {
        setPromoCode(get(campaignDetails, 'couponCode', null));
        onCodeApply(null, campaignDetails);
      }
    }
  }, []);

  const chargeUser = async (event) => {
    setErrorMessage(null);
    setCouponErrorMsg(null);
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) return;

    setIsProcessing(true);
    try {
      let latestClientSecret = clientSecret;
      if (promoCodeId) {
        const data = await applyCouponCode({
          planId: get(planDetail, '_id', ''),
          priceId: get(selectedPlanDetalils, 'priceId', ''),
          promoCodeId,
        });
        const updatedClientSecret = get(data, 'data.clientSecret', '');
        if (updatedClientSecret && updatedClientSecret !== clientSecret) {
          setClientSecret(updatedClientSecret);
          latestClientSecret = updatedClientSecret;
        }
      }
      const { error: submitError } = await elements.submit();
      if (submitError) {
        setIsProcessing(false);
        return;
      }
      if (
        get(promoCodeDetailsRef.current, 'type', null) === 'percent_off' &&
        get(promoCodeDetailsRef.current, 'amount', 0) === 100
      ) {
        const setupIntentResponse = await setupIntent();
        const setupClientSecret = get(setupIntentResponse, 'data.clientSecret');
        if (!setupClientSecret) {
          setErrorMessage('Could not initialize card setup.');
          setIsProcessing(false);
          return;
        }

        const { error: setupError } = await stripe.confirmSetup({
          elements,
          clientSecret: setupClientSecret,
          confirmParams: {
            return_url: window.location.href,
          },
        });

        if (setupError) {
          setErrorMessage(setupError.message);
          setIsProcessing(false);
          return;
        }

        // ✅ After successful card setup
        // dispatch(getSubscription());
        if (isOfferScreen && typeof window !== 'undefined') {
          setTimeout(() => (window.location.href = '/projects'), 1000);
        } else {
          setShowPaymentScreen('confirmPayment');
        }
        setBillingCycle('monthly');
        setIsProcessing(false);
        return;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret: latestClientSecret,
        redirect: 'if_required',
      });
      // const error = {}

      if (paymentIntent.status === 'processing') {
        const data = {
          userMeta: {
            bacsPaymentStatus: 'processing',
          },
        };
        updateUserMeta(data, 'payment');
      }

      if (error) {
        setErrorMessage(get(error, 'message'));
        setIsProcessing(false);
      } else {
        setTimeout(async () => {
          const customerPaymentIntent = await getPaymentIntent();
          const status = get(customerPaymentIntent, 'data[0].status');
          const newClientSecret = get(
            customerPaymentIntent,
            'data[0].client_secret',
          );

          if (
            status === 'requires_action' ||
            status === 'requires_confirmation'
          ) {
            const { error } = await stripe.confirmCardPayment(newClientSecret);
            console.log("67188ed57992fb733cf1ad90================")
            if (error) {
              setErrorMessage(error.message);
              setIsProcessing(false);
            } else {
              dispatch(getSubscription());
              if (isOfferScreen && typeof window !== 'undefined') {
                setTimeout(() => (window.location.href = '/projects'), 1000);
              } else {
                setShowPaymentScreen('confirmPayment');
              }
              setBillingCycle('monthly');
              setIsProcessing(false);
            }
          } else {
            dispatch(getSubscription());
            if (isOfferScreen && typeof window !== 'undefined') {
              setTimeout(() => (window.location.href = '/projects'), 1000);
            } else {
              console.log("67188ed57992fb733cf1ad90======================================")
              setShowPaymentScreen('confirmPayment');
            }
            setIsProcessing(false);
          }
        }, 2000);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
    }
  };

  const onCodeApply = async (event, campaignDetails) => {
    if (event) event.preventDefault();
    setApplyText(false);
    setCouponErrorMsg(null);

    const requestData = {
      subscriptionId,
      ...(campaignDetails
        ? {
          couponCode: campaignDetails.couponCode,
          planId: campaignDetails.planId,
          priceId: campaignDetails.priceId,
        }
        : {
          couponCode: promoCode,
          planId: get(planDetail, '_id', ''),
          priceId: get(selectedPlanDetalils, 'priceId', ''),
        }),
    };

    const data = await couponCodeValidation(requestData);
    if (get(data, 'couponDetails', null) !== null) {
      promoCodeDetailsRef.current = get(data, 'couponDetails', null);
      setPromoCodeDetails(promoCodeDetailsRef.current); // optional, for UI
      setIsCodeApplied(true);
      setApplyText(false);
      setPriceText(get(data, 'amount', ''));
      setPromoCodeId(get(data, 'couponDetails.promoCodeId', ''));
    } else {
      setPriceText(null);
      setApplyText(true);
      setCouponErrorMsg(data);
    }
  };

  const handleCodeChange = (e) => {
    setCouponErrorMsg(null);
    setPromoCode(e.target.value.replace(/\s+/g, ''));
  };

  const handleRemove = async (event) => {
    event.preventDefault();
    setIsCodeRemoving(true);
    setPromoCodeDetails(null);
    setPriceText(null);
    setCouponErrorMsg(null);
    setIsCodeApplied(false);
    setApplyText(true);
    setPromoCodeId(null);
    setIsCodeRemoving(false);
  };

  return (
    <form
      onSubmit={chargeUser}
      className={`${Styles.paymentLabel} ${Styles.paymentForm} p-4 p-sm-4 p-md-5 p-lg-5`}
    >
      <div style={{ width: '100%' }}>
        <p className={`${Styles.formHeading} mb-4 mx-0 mt-0`}>
          Enter payment details
        </p>
        <div className="form-group">
          <label style={{ fontFamily: 'MaisonNeue' }} htmlFor="promo-code">
            Promotion code
          </label>
          <div className="row gx-3 align-items-center mx-0">
            <div className="col-12 col-md pl-0 pr-0 pr-md-2">
              <input
                style={{
                  opacity: !applyText ? '0.6' : '1',
                }}
                value={promoCode}
                className={`${Styles.promotionCodeInput}`}
                disabled={isCodeApplied}
                onChange={handleCodeChange}
                type="text"
                id="promo-code"
                placeholder="Got a promotional code?"
              />
            </div>
            <div className="col-12 col-md-auto px-0">
              <Button
                customClass={Styles.submitButton}
                style={{
                  opacity:
                    !applyText ||
                      !promoCode ||
                      promoCode === '' ||
                      isCodeApplied
                      ? '0.2'
                      : '1',
                }}
                clickHandler={
                  applyText && promoCode && promoCode !== '' && !isCodeApplied
                    ? (e) => onCodeApply(e)
                    : (event) => {
                      event.preventDefault();
                    }
                }
                buttonType="commonBtnClas"
                disabled={!applyText}
                id="submitbutton"
                buttonValue={'Apply'}
                className={`save-btn  px-4 py-2 w-100 w-md-auto ${Styles.applyText}`}
              />
            </div>
          </div>
        </div>
        {isCodeApplied && (
          <Tag
            label={promoCode}
            onRemove={
              inCodeRemoving
                ? (event) => {
                  event.preventDefault();
                }
                : (e) => handleRemove(e)
            }
          />
        )}
        {couponErrorMsg && (
          <p className={Styles.couponErrMsg}> {couponErrorMsg}</p>
        )}

        <div className={Styles.stripePaymentElementForm}>
          <PaymentElement id="card-element" />
        </div>

        <p className={`mt-3 ${Styles.autoRenewText}`}>
          {isOfferScreen
            ? `Auto renews at £${(get(selectedPlanDetalils, 'amount', '') / 100).toFixed(2)}. Cancel anytime.`
            : 'Auto renewal enabled. Cancel anytime.'}
        </p>
        {errorMsg && <p className={Styles.paymentErrMsg}> {errorMsg}</p>}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button
            customClass={Styles.submitButton}
            style={{ opacity: isProcessing ? '0.2' : '1' }}
            // buttonType="primary-button"
            buttonType="commonBtnClas"
            disabled={isProcessing || couponErrorMsg}
            btntype="submit"
            id="submitbutton"
            buttonValue={
              isProcessing ? 'Processing...' : 'UPGRADE TO SMASH PRO'
            }
            className="save-btn mt-auto w-100"
          />
        </div>
      </div>
    </form>
  );
};

const PaymentWrapper = ({
  getClientSecretKey,
  setShowPaymentScreen,
  getPaymentIntent,
  promoCodeDetails,
  removeCouponCode,
  setPriceText,
  priceText,
  selectedPlanDetalils,
  campaignDetails,
  setPromoCodeDetails,
  applyCouponCode,
  setupIntent,
  couponCodeValidation,
  updateUserMeta
}) => {
  const [clientSecret, setClientSecret] = useState(null);
  const [subscriptionId, setSubscriptionId] = useState(null);
  const planDetail = useSelector((state) => state.subscription.proPlan);

  useEffect(() => {
    if (!planDetail || !selectedPlanDetalils) return;

    const payload = {
      id: get(planDetail, '_id', ''),
      priceId: get(selectedPlanDetalils, 'priceId', ''),
    };

    getClientSecretKey(payload)
      .then((data) => {
        const secret = get(data, 'data.clientSecret', '');
        const id = get(data, 'data.id', '');
        if (secret && id) {
          setClientSecret(secret);
          setSubscriptionId(id);
        }
      })
      .catch(console.error);
  }, [getClientSecretKey, planDetail, selectedPlanDetalils]);

  const fonts = [
    {
      family: 'MaisonNeueMono',
      src: 'url(https://smash-fonts.s3.eu-west-2.amazonaws.com/maisonNeue/MaisonNeue-Mono.woff)',
      weight: '400',
    },
  ];

  const appearance = {
    theme: 'stripe',
    fontFamily: 'MaisonNeueMono',
    rules: {
      '.Input': {
        borderRadius: '0px',
        fontFamily: 'MaisonNeueMono',
        display: 'flex',
        padding: '12px 8px',
        alignItems: 'center',
        gap: '10px',
        alignSelf: 'stretch',
        border: '2px solid var(--navy, #05012D)',
        background: 'var(--light, #FFF)',
      },
      '.Input:focus': {
        border: '1px solid #A1A6AD', // Default focus border color
        boxShadow: 'none', // Remove box shadow on focus
      },
      '.Label': {
        color: 'var(--navy, #05012D)',
        fontFamily: 'MaisonNeueMono',
        fontSize: '14px',
        fontStyle: 'normal',
        fontWeight: '400',
        lineHeight: '150%' /* 21px */,
      },
      '.TermsText': {
        // display: 'none',
        fontFamily: 'MaisonNeueMono',
        fontSize: '0px',
      },
    },
    variables: {
      colorPrimary: '#0570de',
      colorText: '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'MaisonNeueMono',
    },
  };

  return !clientSecret ? (
    <Loader isVisible />
  ) : (
    <>
      <div
        className="p-4 p-sm-4 p-md-5 p-lg-5 bg-white d-block d-sm-block d-md-none d-lg-none"
        style={{
          borderBottom: `1px solid #05012D`,
        }}
      >
        <PlanDetails
          priceText={priceText}
          promoCodeDetails={promoCodeDetails}
          selectedPlanDetalils={selectedPlanDetalils}
        />
      </div>
      <Elements
        stripe={stripePromise}
        className={Styles.stripePaymentElement}
        options={{ clientSecret, appearance, fonts }}
      >
        <PaymentScreenContent
          updateUserMeta={updateUserMeta}
          clientSecret={clientSecret}
          subscriptionId={subscriptionId}
          campaignDetails={campaignDetails}
          planDetail={planDetail}
          setShowPaymentScreen={setShowPaymentScreen}
          getPaymentIntent={getPaymentIntent}
          applyCouponCode={applyCouponCode}
          setClientSecret={setClientSecret}
          setPriceText={setPriceText}
          removeCouponCode={removeCouponCode}
          selectedPlanDetalils={selectedPlanDetalils}
          setPromoCodeDetails={setPromoCodeDetails}
          couponCodeValidation={couponCodeValidation}
          setupIntent={setupIntent}
        />
      </Elements>
    </>
  );
};

const mapStateToProps = (state) => ({
  currentPlan: state.subscription.currentPlan,
  isTrial: state.subscription.isTrial,
});

const mapDispatchToProps = (dispatch) => ({
  getClientSecretKey: (payload) => dispatch(getClientSecretKey(payload)),
  getPaymentIntent: (payload) => dispatch(getPaymentIntent(payload)),
  couponCodeValidation: (payload) => dispatch(couponCodeValidation(payload)),
  removeCouponCode: (payload) => dispatch(removeCouponCode(payload)),
  setBillingCycle: (payload) => dispatch(setBillingCycle(payload)),
  applyCouponCode: (payload) => dispatch(applyCouponCode(payload)),
  setupIntent: (payload) => dispatch(setupIntent(payload)),
  updateUserMeta: (payload) => dispatch(updateUserMeta(payload)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(PaymentWrapper));
