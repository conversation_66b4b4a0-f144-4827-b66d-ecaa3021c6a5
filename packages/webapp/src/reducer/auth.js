import axios from 'axios';
import store from 'store';
import _, { get } from 'lodash';
import Router from 'next/router';
import LogRocket from 'logrocket';
import { jwtDecode } from 'jwt-decode';
import { pushToDataLayer } from 'lib/commonUtil';
import { pushFbEvent, trackSubmitApplication } from 'utils/fbPixel';
import { error } from 'sharedComponents/Alerts/error';
import { toast } from 'react-toastify';
import mixpanelAnalytics from 'lib/mixpanel';

/**
 * Set cached route
 *
 * @param {string} route - route
 * @returns {object} - object.
 */
export function setCachedRoute(route = '') {
  return {
    type: 'SET_CACHED_ROUTE',
    payload: route,
  };
}

/**
 * set login form true when startLogin start
 *
 * @type {boolean}
 */
export function setLoginForm(showLoginForm = true) {
  return {
    type: 'SET_LOGIN_FORM',
    payload: showLoginForm,
  };
}

/**
 * set user journey for film maker and decision maker
 *
 * @type {boolean}
 */
export function setUserJourney(userJourney) {
  return {
    type: 'SET_USER_JOURNEY',
    payload: userJourney,
  };
}
/**
 * set login form true when gettyimage start
 *
 * @type {boolean}
 */
export function setGettyLoginForm(hideLoginForm) {
  return {
    type: 'SET_GETTY_LOGIN_FORM',
    payload: hideLoginForm,
  };
}

/**
 * set getty token expiry
 *
 * @type {boolean}
 */
export function setGettyExpiry(data) {
  const gettyTokenExpiry = new Date();
  gettyTokenExpiry.setSeconds(gettyTokenExpiry.getSeconds() + (data - 10));
  return {
    type: 'SET_GETTY_EXPIRY',
    payload: gettyTokenExpiry,
  };
}

/**
 * Set login form true when startLogin start
 * Set a message for user after login
 *
 * @type {string}
 */
export function setMsg(showMsg) {
  return {
    type: 'SET_MSG',
    payload: showMsg,
  };
}
/**
 * set user meta data object
 *
 * @type {object}
 */
export function setsignUpMeta(signUpMeta) {
  return {
    type: 'SET_SIGNUP_META',
    payload: signUpMeta,
  };
}

/**
 * set user meta data object
 *
 * @type {object}
 */
export function setDiscovererEmail(email) {
  return {
    type: 'SET_DISCOVERER_EMAIL',
    payload: email,
  };
}

/**
 * Function to enable loader
 *
 * @type {boolean} status
 */
export function setLoadingStatusGetty(boolVal) {
  return {
    type: 'SET_LOADING_GETTY',
    payload: boolVal,
  };
}

/**
 * set login form getty data
 *
 * @type {boolean}
 */
export function setGettyAccessToken(data) {
  return {
    type: 'SET_GETTY_ACCESS_TOKEN',
    payload: data,
  };
}

/**
 * Set user data object
 *
 * @type {object}
 */
export function setUserData(showData) {
  return {
    type: 'SET_USER_DATA',
    payload: showData,
  };
}

/**
 * Set user data object
 *
 * @type {string} id
 */
export function setCollaboratorProjectId(id) {
  return {
    type: 'SET_COLLABORATOR_PROJECT_ID',
    payload: id,
  };
}

/**
 * Set an token.
 *
 * @type {string}
 */
export function setGettyImageData(data) {
  return {
    type: 'SET_GETTY_IMAGE_DATA',
    payload: data,
  };
}

/**
 * Set user data object
 *
 * @type {string}
 */
export function setUserRedirectUrl(url = {}) {
  return {
    type: 'SET_REDIRECT_URL',
    payload: url,
  };
}

/**
 * Set an token.
 *
 * @type {string}
 */
export function setToken(token) {
  return {
    type: 'SET_TOKEN',
    payload: token,
  };
}

/**
 * Set an error message if any .
 *
 * @type {string}
 */
export function setError(showError) {
  return {
    type: 'SET_ERROR',
    payload: showError,
  };
}

/**
 * Set snap url .
 *
 * @type {string}
 */
export function setSnapRedirect(url) {
  return {
    type: 'SET_USER_SNAP_REDIRECTION',
    payload: url,
  };
}

/**
 * Set snap journey .
 *
 * @type {string}
 */
export function setSnapJourney(authType) {
  return {
    type: 'SET_USER_SNAP_JOURNEY',
    payload: authType,
  };
}

/**
 * Set collaborator sign error message .
 *
 * @type {string}
 */
export function setCollaboratorSigninErrMsg(msg) {
  return {
    type: 'SET_COLLABORATOR_SIGNIN_ERROR_MSG',
    payload: msg,
  };
}

/**
 * Set profile status
 *
 * @type {boolean} status
 */
export function setEditProfileStatus(showStatus) {
  return {
    type: 'SET_EDIT_PROFILE_STATUS',
    payload: showStatus,
  };
}

/**
 * Sets the user email in the redux.
 *
 * @param {string} email - The user email to be set.
 * @returns {void} This function does not return anything.
 */
export function setEmail(email) {
  return {
    type: 'SET_EMAIL',
    payload: email,
  };
}

/**
 * Sets the auth0 refresh token in the redux.
 *
 * @param {string} refreshToken - The auth0 token to be set.
 * @returns {void} This function does not return anything.
 */
export function setRefreshToken(refreshToken) {
  return {
    type: 'SET_REFRESH_TOKEN',
    payload: refreshToken,
  };
}

/**
 * To send login otp on given email
 *
 * @param {any} values - contain login form value.
 * @returns {any} This function return API response.
 */
export const startLoginWithOtp = (values) => (dispatch) => {
  dispatch(setEmail(get(values, 'email', '')));
  return axios({
    url: `${process.env.ImApiBaseUrl}v1/auth/sendOTPToEmail`,
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'post',
    data: values,
    responseType: 'json',
  })
    .then((response) => {
      return response;
    })
    .catch((err) => {
      const status = get(err, 'response.status', false);
      return status;
    });
};

/**
 * It decide a valid route and redirect user to at that route.
 *
 * @returns {*} window url
 */
export const redirectUser = () => (dispatch, getState) => {
  const { userJourney, collaboratorProjectId } = getState().auth;
  // Redirect user on sign in.
  if (userJourney === 'signIn') {
    if (!collaboratorProjectId) {
      dispatch(setCollaboratorProjectId(null));
      dispatch(redirectUserFilmMaker());
    } else {
      Router.push(`/collaborator/verify/${collaboratorProjectId}`);
    }
  }
  // if user journey is filmMakerSignUp
  if (userJourney === 'filmMakerSignUp') {
    dispatch(redirectUserFilmMaker());
    dispatch(redirectUserFilmMaker());
  } else if (userJourney === 'collaboratorSignUp') {
    dispatch(redirectUserFilmMaker());
  }
};

/**
 * It decide a valid route and redirect user to at that route.
 *
 * @returns {*} window url
 */
export const redirectUserDecisionMaker = () => (dispatch, getState) => {
  const { userData, signUpMeta } = getState().auth;
  const redirectUrl = _.get(signUpMeta, 'userMeta.redirectUrlDecisionMaker');
  const url = _.get(userData, 'userMeta.redirectUrlDecisionMaker', redirectUrl);
  // redirects user to which ever screen was left on last
  if (url) {
    Router.push(url);
  } else {
    Router.push('/discoverer/profile/getStarted');
  }
};

/**
 * It decide a valid route and redirect user to at that route.
 *
 * @returns {*} window url
 */
export const redirectUserFilmMaker = () => (dispatch, getState) => {
  const { userData, signUpMeta } = getState().auth;
  const redirectUrl = _.get(signUpMeta, 'userMeta.redirectUrl');
  const url = _.get(userData, 'userMeta.redirectUrl', redirectUrl);
  const redirectUrlDm = _.get(
    userData,
    'userMeta.redirectUrlDecisionMaker',
    false,
  );
  // redirects user to which ever screen was left on last
  if (url) {
    Router.push(url);
  } else if (redirectUrlDm) {
    Router.push(redirectUrlDm);
  } else {
    Router.push('/profile/getStarted');
  }
};

/**
 * To delete getty token if it expires.
 *
 * @returns {*} window url
 */
export const tokenValidation = () => (dispatch, getState) => {
  const getty = getState().auth.gettyExpiresIn;
  if (getty && getty.access_token) {
    const expiresAt = new Date(getty.expiresAt);
    const currentAt = new Date();
    if (expiresAt > currentAt) {
      store.remove('Getty');
    }
  }
};

/**
 * Set user details
 *
 * @type {string} token
 * @type {object} user data
 */
export const setUserDetails = (token, user) => async (dispatch, getState) => {
  const oldData = getState().auth.userData;
  await dispatch(setUserData(user));
  await dispatch(setToken(token));
  if (user.userMeta !== undefined) {
    await dispatch(setUserRedirectUrl(user.userMeta));
  }
  // check for new login /signup and initialize log rocket
  if (
    _.isEmpty(oldData) &&
    !_.isEmpty(user) &&
    process.env.LogRocketEnabled === 'true'
  ) {
    LogRocket.identify(user._id, {
      name: _.get(user, 'name.fullName'),
      email: user.email,
    });
  }
  // redirect to next page
  dispatch(redirectUser());
};

/**
 * Api for updateUserMeta
 *
 * @type {object} status
 */
export const updateUserMeta =
  (data, source = false) =>
    (dispatch, getState) => {
      const token = getState().auth.token;
      return axios({
        url: `${process.env.ImApiBaseUrl}v2/user/metadata`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getState().auth.token}`,
        },
        method: 'patch',
        data,
        responseType: 'json',
      }).then((response) => {
        console.log("source=============", source)
        if (source !== 'payment') {
          dispatch(setUserData(response.data.data));
          if (!source) {
            dispatch(setUserDetails(token, response.data.data));
          }
        }
      });
    };

/**
 * Api for creating user
 *
 * @type {string} status
 */
export const createUser = (token) => (dispatch, getState) => {
  dispatch(setToken(token));
  const data = getState().auth.signUpMeta;

  return axios({
    url: `${process.env.ImApiBaseUrl}user/create`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    method: 'post',
    responseType: 'json',
  }).then((response) => {
    const userData = get(response, 'data.data');
    dispatch(updateUserMeta(data));
    pushFbEvent('Submit Application');
    trackSubmitApplication('Submit Application');
    // Mixpanel Analytics: Track User Creation
    mixpanelAnalytics('user_created', {
      userId: get(userData, 'user._id', ''), // Assuming `userId` comes from the response
      email: get(userData, 'user.email', ''), // Assuming `email` exists in `data`
    });
  });
};

/**
 * Api for creating user
 *
 * @type {string} status
 */
export const handleOpsDiscovererSignup =
  (token, id) => (dispatch, getState) => {
    dispatch(setToken(token));
    const data = getState().auth.signUpMeta;
    const decoded = jwtDecode(token);
    return axios({
      url: `${process.env.ImApiBaseUrl}user/${id}`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      data: { auth0Id: decoded.sub },
      method: 'patch',
      responseType: 'json',
    }).then(() => {
      dispatch(updateUserMeta(data));
      dispatch(setDiscovererEmail(null));
    });
  };

/**
 * send magic link to user to authenticate.
 *
 * @type {object} status
 */
export const startLogin =
  (data, authFlow = 'signin') =>
    (dispatch, getState) => {
      const { userJourney } = getState().auth;
      dispatch(setLoginForm(true));
      return axios({
        url: `${process.env.ImApiBaseUrl}auth/sendMagicLink`,
        headers: {
          'Content-Type': 'application/json',
        },
        method: 'post',
        data: {
          email: data.email,
          authFlow,
          state: userJourney,
        },
        responseType: 'json',
      })
        .then(() => {
          dispatch(setLoginForm(false));
          dispatch(setMsg(data.email));
        })
        .catch((err) => {
          const errMsg = _.get(err.response, 'data.message', '');
          dispatch(setLoginForm(true));
          dispatch(setMsg(errMsg));
        });
    };

/**
 * check existing user meta and update if user is both film maker and decision maker
 *
 * @returns {*} window url
 */
export const addUserType = () => (dispatch, getState) => {
  const { userData, signUpMeta } = getState().auth;

  let url = '';
  // if journey is decisionmaker signup or film maker sign up
  const { userJourney } = getState().auth;
  if (userJourney === 'filmMakerSignUp') {
    let data = {};

    url = _.get(
      userData,
      'userMeta.redirectUrl',
      signUpMeta.userMeta.redirectUrl,
    );
    data = {
      userMeta: {
        redirectUrl: url,
      },
    };

    // to update meta data
    dispatch(updateUserMeta(data));
  }
};
/**
 * check existing user meta and update if user is both film maker and decision maker
 *
 * @returns {*} window url
 */
export const isUserExistSignIn = (data) => (dispatch) => {
  return axios({
    url: `${process.env.ImApiBaseUrl}v1/auth/isUserExists?email=${data.email}`,
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch();
      return response.data;
    })
    .catch(() => { });
};

/**
 * To verify auth0 opt received on email
 *
 * @param {string} otp - Auth0 verification otp.
 * @returns {any} This function return API response.
 */

export const verifyOtp = (otp) => (dispatch, getState) => {
  const url = `${process.env.ImApiBaseUrl}v1/auth/verifyOtp`;
  const { email } = getState().auth;
  return axios({
    url,
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'post',
    data: {
      email: email,
      otp,
    },
    responseType: 'json',
  })
    .then(async (response) => {
      const token = get(response, 'data.data.token', '');
      const user = get(response, 'data.data.user', false);
      const redirectUrl = get(user, 'userMeta.redirectUrl', false);

      await dispatch(setToken(token));

      if (!user) {
        await dispatch(createUser(token));
        return true;
      } else {
        await dispatch(setUserDetails(token, user));
        const { userData } = getState().auth;
        const { currentFlow } = getState().user;

        if (currentFlow === 'signUpFlow') {
          await dispatch(setCachedRoute('/callouts/projectsList'));
        }

        if (userData) {
          await dispatch(addUserType());
          // Track user type addition
          mixpanelAnalytics('user_type_added', {
            email,
            user,
          });
        }

        // Track redirection if applicable
        if (redirectUrl) {
          mixpanelAnalytics('sign_in_successfully', {
            email,
            redirectUrl,
            pageName: 'sign_in',
            user,
          });
        }

        return redirectUrl || '/mydashboard';
      }
    })
    .catch((error) => {
      // Track OTP verification failure
      mixpanelAnalytics('otp_verification_failure', {
        email,
        error: error.message,
      });
      return false;
    });
};

/**
 * verify user api
 *
 * @type {object} status
 */
export const verify = (data) => (dispatch, getState) => {
  return axios({
    url: `${process.env.ImApiBaseUrl}user/detail`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${data.id_token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then(async (response) => {
      await dispatch(setUserDetails(data.id_token, response.data.data));
      // userrtype update when user already exist decision maker

      const { userData } = getState().auth;
      if (userData) {
        dispatch(addUserType());
      }
    })
    .catch((error) => {
      if (error.response.status === 404) {
        const { userData, signUpMeta, userJourney } = getState().auth;
        const redirectUrl = _.get(signUpMeta, 'userMeta.redirectUrl');
        const url = _.get(userData, 'userMeta.redirectUrl', redirectUrl);
        if (userJourney === 'collaboratorSignUp') {
          if (url === undefined || url === null) {
            Router.push({
              pathname: '/SignInError',
              query: {
                errorType: 'crossBrowserLink',
              },
            });
          } else {
            dispatch(createUser(data.id_token));
          }
        } else {
          if (url === undefined || url === null) {
            Router.push({
              pathname: '/SignInError',
              query: {
                errorType: 'crossBrowserLink',
              },
            });
          } else {
            dispatch(createUser(data.id_token));
          }
        }
      } else {
        Router.push('/SignInError');
      }
    });
};

/**
 * Getty search image according to its condition
 *
 * @param {*} searchOption search option
 * @returns {void} void
 */
export const gettyImagesSearch = (searchOption) => (dispatch, getState) => {
  let url = `${process.env.GettyApiBaseUrl}/v3/search/images`;
  const headers = {
    'Content-Type': 'application/json',
    'Api-Key': `${process.env.GettyAuth0ClientId}`,
  };

  /* Get token form state if present */
  const token = getState().auth.gettyAccessToken;
  if (token) {
    /* Append url with option to with token getty search api */
    url += `?fields=${searchOption.fields || 'preview'}&page=${searchOption.page || 1
      }&page_size=${searchOption.page_size || process.env.GettyLimit}&phrase=${searchOption.searchText
      }`;

    const expiresIn = getState().auth.gettyExpiresIn;
    const expiresAt = new Date(expiresIn);
    const currentAt = new Date();
    /* Check if current time is greater than expiry time refresh token api called */
    if (expiresAt > currentAt) {
      // if token is valid
      headers.Authorization = `Bearer ${getState().auth.gettyAccessToken}`;
      dispatch(getGettyImages(url, headers));
    } else {
      // when token is expire
      dispatch(getGettyImageAuth0Token(url, headers));
    }
  } else {
    // without token call getty image search
    url += `/creative?fields=${searchOption.fields || 'preview'}&page=${searchOption.page || 1
      }&page_size=${searchOption.page_size || process.env.GettyLimit}&phrase=${searchOption.searchText
      }`;
    dispatch(getGettyImages(url, headers));
  }
};

/**
 * Create code verifier, code challenge and redirect to getty auth0 authorization page
 *
 * @param {*} data getty image data
 * @returns {*} void
 */
export const gettyImagesAuthenticate = (data) => (dispatch, getState) => {
  dispatch(setLoadingStatusGetty(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/getty/login`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    data,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setGettyAccessToken(response.data.data.access_token));
      dispatch(setGettyExpiry(response.data.data.expires_in));
      toast.success('Login Successful');
      dispatch(setGettyLoginForm(true));
      pushToDataLayer({
        action: 'getty login success',
        category: 'Project moodboard',
        label: 'Login into getty sucess',
      });
      dispatch(
        gettyImagesSearch({
          searchText: 'random',
          fields: 'preview,summary_set',
          page: 1,
        }),
      );
    })
    .catch(() => {
      dispatch(setLoadingStatusGetty(false));
      error('Username or Password Incorrect');
    });
};

/**
 * Get search getty images
 *
 * @param {*} url getty url
 * @param {*} headers getty headers
 * @returns {*} getty images
 */
export const getGettyImages = (url, headers) => (dispatch) => {
  dispatch(setLoadingStatusGetty(true));
  return axios({
    url,
    headers,
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setGettyImageData(response.data));
      dispatch(setLoadingStatusGetty(false));
    })
    .catch(() => {
      dispatch(setLoadingStatusGetty(false));
    });
};

/**
 * Get getty image auth0 token
 *
 * @param {*} url getty image search url
 * @param {*} headers getty header
 * @returns {*} token
 */
export const getGettyImageAuth0Token =
  (url, headers) => (dispatch, getState) => {
    return axios({
      url: `${process.env.SmashApiBaseUrl}/getty/token`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'get',
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setGettyAccessToken(response.data.data.access_token));
        dispatch(setGettyExpiry(response.data.data.expires_in));
        headers.Authorization = `Bearer ${response.data.data.access_token}`;
        dispatch(getGettyImages(url, headers));
      })
      .catch(() => { });
  };

/**
 * When user logout then clear all local storage data and redirect to home page.
 *
 * @returns {*} window url
 * @param id
 */
export const logout = (id) => (dispatch, getState) => {
  const { userJourney } = getState().auth;
  let logoutRedirect = '/';
  dispatch(setUserData({}));
  localStorage.clear();
  store.clearAll();
  dispatch(setToken(''));
  dispatch(setCachedRoute(''));
  if (userJourney === 'collaboratorSignUp') {
    dispatch(setCollaboratorProjectId(id));
    logoutRedirect = '/collaborator/signUp';
    Router.push(`${logoutRedirect}`);
  } else {
    window.location.href = `${window.location.origin}${logoutRedirect}`;
  }
};

/* initialize state and auth reducer */
export const initialState = {
  email: '',
  refreshToken: '',
  showLoginForm: true,
  isLoadingGetty: false,
  message: false,
  error: '',
  userData: {},
  editStatus: false,
  token: '',
  signUpMeta: {},
  redirectUrl: {},
  gettyImageData: {},
  gettyAccessToken: '',
  gettyExpiresIn: '',
  hideLoginForm: false,
  userJourney: '', // filmMakerSignIn filmMakerSignUp decisionMakerSignIn decisionMakerSignUp
  snapRedirectUrl: false,
  snapJourney: null,
  collaboratorProjectId: null,
  collaboratorSigninErrMsg: false,
  discovererEmail: null,
  cachedRoute: '',
};

/**
 * To set auth object in redux
 *
 * @param {any} state - Redux state
 * @param {any} action - Redux action
 * @returns {any} This function return redux auth state.
 */
const auth = (state = initialState, action) => {
  switch (action.type) {
    case 'SET_EMAIL':
      return {
        ...state,
        email: action.payload,
      };
    case 'SET_REFRESH_TOKEN':
      return {
        ...state,
        refreshToken: action.payload,
      };
    case 'SET_LOGIN_FORM':
      return {
        ...state,
        showLoginForm: action.payload,
      };
    case 'SET_LOADING_GETTY':
      return {
        ...state,
        isLoadingGetty: action.payload,
      };
    case 'SET_MSG':
      return {
        ...state,
        message: action.payload,
      };
    case 'SET_USER_DATA':
      return {
        ...state,
        userData: action.payload,
      };
    case 'SET_TOKEN':
      return {
        ...state,
        token: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };
    case 'SET_EDIT_PROFILE_STATUS':
      return {
        ...state,
        editStatus: action.payload,
      };
    case 'SET_USER_JOURNEY':
      return {
        ...state,
        userJourney: action.payload,
      };
    case 'SET_SIGNUP_META':
      return {
        ...state,
        signUpMeta: action.payload,
      };
    case 'SET_REDIRECT_URL':
      return {
        ...state,
        redirectUrl: action.payload,
      };
    case 'SET_GETTY_IMAGE_DATA':
      return {
        ...state,
        gettyImageData: action.payload,
      };
    case 'SET_GETTY_ACCESS_TOKEN':
      return {
        ...state,
        gettyAccessToken: action.payload,
      };
    case 'SET_GETTY_EXPIRY':
      return {
        ...state,
        gettyExpiresIn: action.payload,
      };
    case 'SET_GETTY_LOGIN_FORM':
      return {
        ...state,
        hideLoginForm: action.payload,
      };
    case 'SET_USER_SNAP_REDIRECTION':
      return {
        ...state,
        snapRedirectUrl: action.payload,
      };
    case 'SET_USER_SNAP_JOURNEY':
      return {
        ...state,
        snapJourney: action.payload,
      };
    case 'SET_COLLABORATOR_PROJECT_ID':
      return {
        ...state,
        collaboratorProjectId: action.payload,
      };
    case 'SET_COLLABORATOR_SIGNIN_ERROR_MSG':
      return {
        ...state,
        collaboratorSigninErrMsg: action.payload,
      };
    case 'SET_DISCOVERER_EMAIL':
      return {
        ...state,
        discovererEmail: action.payload,
      };
    case 'SET_CACHED_ROUTE':
      return {
        ...state,
        cachedRoute: action.payload,
      };
    default:
      return state;
  }
};

export default auth;
